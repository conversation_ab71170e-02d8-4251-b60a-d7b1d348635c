# Social Media Implementation Guide

## Overview
This implementation provides functionality to display coach social media links, specifically Instagram links, from the API response. The system fetches social media data from the API and displays it in various components.

## API Integration

### API Endpoint
```
GET ${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-social-media?userId=${userId}
```

### Expected API Response Format
```json
[
  {
    "id": 7,
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "Instagram",
    "socialMediaLink": "https://instagram.com/coach_profile",
    "isHidden": false,
    "createdAt": "2025-08-03T09:04:17.000Z",
    "updatedAt": "2025-08-03T09:04:17.000Z"
  },
  {
    "id": 8,
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "X",
    "socialMediaLink": "https://X.com/in/coach_profile",
    "isHidden": false,
    "createdAt": "2025-08-03T09:04:17.000Z",
    "updatedAt": "2025-08-03T09:04:17.000Z"
  }
]
```

## Redux Store Integration

### Slice Update
The `fetchCoachSocialMediaLinks.fulfilled` case in `coachProfileSlice.ts` has been updated to properly merge API data with the existing social media list:

```typescript
.addCase(fetchCoachSocialMediaLinks.fulfilled, (state, action) => {
  state.loading = false;
  
  // Create a map of API data for easy lookup
  const apiDataMap = new Map();
  action.payload?.forEach((item: any) => {
    apiDataMap.set(item.socialMedia.toLowerCase(), {
      link: item.socialMediaLink,
      isHidden: item.isHidden,
    });
  });

  // Update the existing coachSocialMediaList with API data
  state.coachSocialMediaList = state.coachSocialMediaList.map((item) => {
    const apiData = apiDataMap.get(item.id.toLowerCase());
    return {
      ...item,
      link: apiData?.link || item.link,
      isEnable: apiData ? !apiData.isHidden : item.isEnable,
    };
  });
})
```

## Utility Functions

### Social Media Helpers (`src/utils/socialMediaHelpers.ts`)

#### `getInstagramLink(socialMediaList: EachSocialMediaItem[]): string`
Extracts the Instagram link from the social media list if it exists and is not hidden.

#### `getSocialMediaLink(socialMediaList: EachSocialMediaItem[], platform: string): string`
Extracts a specific social media link by platform name.

#### `getVisibleSocialMediaLinks(socialMediaList: EachSocialMediaItem[]): EachSocialMediaItem[]`
Returns all visible social media links that have values.

## Components

### InstagramLink Component (`src/components/common/InstagramLink.tsx`)
A reusable component to display Instagram links with customizable options.

**Props:**
- `socialMediaList: EachSocialMediaItem[]` - Array of social media items
- `className?: string` - Additional CSS classes
- `showIcon?: boolean` - Whether to show Instagram icon (default: true)
- `showLabel?: boolean` - Whether to show "Instagram:" label (default: false)

**Usage:**
```tsx
import InstagramLink from '@/components/common/InstagramLink';

<InstagramLink 
  socialMediaList={coachSocialMediaList} 
  showIcon={true}
  showLabel={false}
  className="justify-center text-sm"
/>
```

### CoachSocialMediaDisplay Component (`src/components/coach/coachProfile/CoachSocialMediaDisplay.tsx`)
A comprehensive component that displays all social media links with special highlighting for Instagram.

## Integration Examples

### In CoachAboutCard Component
The Instagram link has been integrated into the coach profile card:

```tsx
{/* Instagram Link Display */}
<div className="mt-2">
  <InstagramLink 
    socialMediaList={coachSocialMediaList} 
    showIcon={true}
    showLabel={false}
    className="justify-center text-sm"
  />
</div>
```

### Using Helper Functions
```tsx
import { getInstagramLink, getSocialMediaLink } from '@/utils/socialMediaHelpers';

const MyComponent = () => {
  const { coachSocialMediaList } = useSelector((state: RootState) => state.coachProfile);
  
  const instagramLink = getInstagramLink(coachSocialMediaList);
  const xLink = getSocialMediaLink(coachSocialMediaList, 'x');
  
  return (
    <div>
      {instagramLink && (
        <a href={instagramLink} target="_blank" rel="noopener noreferrer">
          Follow on Instagram
        </a>
      )}
    </div>
  );
};
```

## Testing

### Test Page
A test page has been created at `/test-social-media` to demonstrate all functionality:
- Raw API data display
- Instagram link component
- Helper function results
- Full social media display component

### How to Test
1. Run the application: `npm run dev`
2. Navigate to `http://localhost:3001/test-social-media`
3. Check the coach profile page to see the Instagram link in the about card

## Key Features

1. **Automatic Data Merging**: API data is automatically merged with the existing social media structure
2. **Visibility Control**: Links are only shown if `isHidden` is false and the link exists
3. **Reusable Components**: Components can be used anywhere in the application
4. **Type Safety**: Full TypeScript support with proper interfaces
5. **Flexible Display**: Customizable icon and label display options

## Notes

- The Instagram link will only display if it exists in the API response and `isHidden` is false
- The system is case-insensitive when matching social media platforms
- All components handle empty or missing data gracefully
- The implementation follows the existing codebase patterns and conventions
